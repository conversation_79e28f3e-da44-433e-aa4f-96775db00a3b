<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;

class Category extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'ar_name',
        'en_name',
        'fr_name',
        'ar_description',
        'en_description',
        'fr_description',
        'image',
        'is_active',
        'sort_order',
        'slug'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    // Get localized name based on current app locale
    public function getNameAttribute()
    {
        $locale = App::getLocale();
        return $this->{$locale.'_name'} ?? $this->en_name;
    }

    // Get localized description based on current app locale
    public function getDescriptionAttribute()
    {
        $locale = App::getLocale();
        return $this->{$locale.'_description'} ?? $this->en_description;
    }

    // Scope for active categories
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope for ordered categories
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }
}
